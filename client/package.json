{"name": "educasheer", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:prod": "npm run optimize-images && vite build", "optimize-images": "node optimize-images.js", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@react-oauth/google": "^0.12.1", "axios": "^1.7.9", "framer-motion": "^12.9.2", "html5-qrcode": "^2.3.8", "lucide-react": "^0.503.0", "motion": "^12.0.0", "qrcode.react": "^3.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "react-icons": "^5.4.0", "react-qr-reader": "^3.0.0-beta-1", "react-router-dom": "^7.1.3", "react-tsparticles": "^2.12.2", "tsparticles": "^3.8.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "sharp": "^0.32.6", "tailwindcss": "^3.4.17", "terser": "^5.26.0", "vite": "^6.0.5", "vite-plugin-compression": "^0.5.1"}}