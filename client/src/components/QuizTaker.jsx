import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Fa<PERSON>lock, FaExclamationTriangle, FaCheck, FaTimes, FaArrowLeft, FaArrowRight, FaQuestionCircle, FaListAlt, FaTrophy, FaEdit, FaFileAlt, FaBookmark, FaTrash, FaBars, FaEye } from 'react-icons/fa';
import { quizAPI } from '../services/quizAPI';
import { toast } from 'react-hot-toast';
import '../styles/quiz-enhancements.css';

const QuizTaker = () => {
  const { courseId, quizId } = useParams();
  const navigate = useNavigate();
  
  const [quiz, setQuiz] = useState(null);
  const [attempt, setAttempt] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState([]);
  const [markedForReview, setMarkedForReview] = useState([]);
  const [timeLeft, setTimeLeft] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showConfirmSubmit, setShowConfirmSubmit] = useState(false);
  const [showQuestionPanel, setShowQuestionPanel] = useState(false);
  const [showClearConfirm, setShowClearConfirm] = useState(false);
  
  const timerRef = useRef(null);
  
  useEffect(() => {
    startQuiz();

    return () => {
      // Cleanup function to prevent memory leaks
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [quizId]);
  
  const startQuiz = async () => {
    try {
      setLoading(true);
      setError(''); // Clear any previous errors

      // Clear any existing timer
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }

      // Reset state
      setQuiz(null);
      setAttempt(null);
      setAnswers([]);
      setMarkedForReview([]);
      setCurrentQuestionIndex(0);
      setTimeLeft(null);
      setIsSubmitting(false);
      setShowConfirmSubmit(false);
      setShowQuestionPanel(false);
      setShowClearConfirm(false);

      // Start a new quiz attempt
      const response = await quizAPI.startQuizAttempt(quizId);

      // Validate response structure
      if (!response || !response.data || !response.data.data) {
        throw new Error('Invalid response structure from server');
      }

      let newAttempt, quizData;

      // Handle two different response structures:
      // 1. New attempt: { attempt: {...}, quiz: {...} }
      // 2. Continuing attempt: just the attempt object directly
      if (response.data.data.attempt && response.data.data.quiz) {
        // New attempt case
        newAttempt = response.data.data.attempt;
        quizData = response.data.data.quiz;
      } else if (response.data.data._id || response.data.data.id) {
        // Continuing existing attempt case
        newAttempt = response.data.data;
        // We need to fetch the quiz data separately
        const quizResponse = await quizAPI.getQuizById(newAttempt.quiz);
        quizData = quizResponse.data.data;
      } else {
        throw new Error('Unexpected response structure from server');
      }

      // Validate attempt data - check for _id or id field
      if (!newAttempt || (!newAttempt._id && !newAttempt.id)) {
        throw new Error('Invalid attempt data received from server');
      }

      // Validate quiz data
      if (!quizData) {
        throw new Error('No quiz data received from server');
      }

      if (!quizData.questions || !Array.isArray(quizData.questions)) {
        throw new Error('Invalid quiz questions data received from server');
      }

      if (quizData.questions.length === 0) {
        throw new Error('This quiz has no questions');
      }

      // Validate that all questions have required properties
      for (let i = 0; i < quizData.questions.length; i++) {
        const question = quizData.questions[i];
        if (!question._id) {
          throw new Error(`Question ${i + 1} is missing required ID`);
        }
      }

      setQuiz(quizData);
      setAttempt(newAttempt);

      // Initialize answers array - check if we have existing answers from a previous attempt
      let initialAnswers, initialMarkedForReview;
      if (newAttempt.answers && newAttempt.answers.length > 0) {
        // Restore previous answers for continuing attempt
        initialAnswers = quizData.questions.map(question => {
          const existingAnswer = newAttempt.answers.find(ans =>
            ans.question?.toString() === question._id?.toString() ||
            ans.questionId?.toString() === question._id?.toString()
          );

          return {
            questionId: question._id,
            selectedOptions: existingAnswer?.selectedOptions || [],
            textAnswer: existingAnswer?.textAnswer || ''
          };
        });

        // Restore marked for review status
        initialMarkedForReview = quizData.questions.map(question => {
          const existingAnswer = newAttempt.answers.find(ans =>
            ans.question?.toString() === question._id?.toString() ||
            ans.questionId?.toString() === question._id?.toString()
          );
          return existingAnswer?.markedForReview || false;
        });
      } else {
        // Create fresh answers for new attempt
        initialAnswers = quizData.questions.map(question => ({
          questionId: question._id,
          selectedOptions: [],
          textAnswer: ''
        }));

        // Initialize marked for review array
        initialMarkedForReview = new Array(quizData.questions.length).fill(false);
      }

      setAnswers(initialAnswers);
      setMarkedForReview(initialMarkedForReview);

      // Set up timer if quiz has time limit
      if (quizData.timeLimit && quizData.timeLimit > 0) {
        let timeLeft;

        if (newAttempt.startTime) {
          // Calculate remaining time for continuing attempt
          const startTime = new Date(newAttempt.startTime);
          const currentTime = new Date();
          const elapsedSeconds = Math.floor((currentTime - startTime) / 1000);
          const totalTimeLimit = quizData.timeLimit * 60; // convert to seconds
          timeLeft = Math.max(0, totalTimeLimit - elapsedSeconds);
        } else {
          // New attempt
          timeLeft = quizData.timeLimit * 60; // convert to seconds
        }

        setTimeLeft(timeLeft);

        if (timeLeft > 0) {
          timerRef.current = setInterval(() => {
            setTimeLeft(prevTime => {
              if (prevTime <= 1) {
                clearInterval(timerRef.current);
                submitQuiz();
                return 0;
              }
              return prevTime - 1;
            });
          }, 1000);
        } else {
          // Time has already expired
          setTimeout(() => submitQuiz(), 100);
        }
      }

    } catch (err) {
      console.error('Error starting quiz:', err);
      const errorMessage = err.message || 'Failed to start quiz. Please try again.';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };
  
  const handleOptionSelect = (optionId) => {
    if (!quiz || !quiz.questions || !answers || currentQuestionIndex >= quiz.questions.length) {
      console.error('Invalid state for option selection');
      return;
    }

    const currentQuestion = quiz.questions[currentQuestionIndex];
    const currentAnswer = answers[currentQuestionIndex];

    if (!currentQuestion || !currentAnswer) {
      console.error('Current question or answer is undefined');
      return;
    }

    if (currentQuestion.type === 'multiple_choice') {
      // For multiple choice, toggle the selection
      const newSelectedOptions = currentAnswer.selectedOptions.includes(optionId)
        ? currentAnswer.selectedOptions.filter(id => id !== optionId)
        : [...currentAnswer.selectedOptions, optionId];

      updateAnswer(currentQuestionIndex, { selectedOptions: newSelectedOptions });
    } else if (currentQuestion.type === 'true_false') {
      // For true/false, select only one option
      updateAnswer(currentQuestionIndex, { selectedOptions: [optionId] });
    }
  };
  
  const handleTextAnswerChange = (e) => {
    updateAnswer(currentQuestionIndex, { textAnswer: e.target.value });
  };
  
  const updateAnswer = (index, newValues) => {
    const updatedAnswers = [...answers];
    updatedAnswers[index] = { ...updatedAnswers[index], ...newValues };
    setAnswers(updatedAnswers);
  };
  
  const goToNextQuestion = () => {
    if (!quiz || !quiz.questions) {
      console.error('Quiz data not available for navigation');
      return;
    }
    if (currentQuestionIndex < quiz.questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  const goToPreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const toggleMarkForReview = () => {
    const updatedMarked = [...markedForReview];
    updatedMarked[currentQuestionIndex] = !updatedMarked[currentQuestionIndex];
    setMarkedForReview(updatedMarked);

    if (updatedMarked[currentQuestionIndex]) {
      toast.success('Question marked for review');
    } else {
      toast.success('Question unmarked for review');
    }
  };

  const clearCurrentResponse = () => {
    if (!quiz || !quiz.questions || !answers) return;

    const currentQuestion = quiz.questions[currentQuestionIndex];
    const currentAnswer = answers[currentQuestionIndex];

    // Check if there's anything to clear
    const hasAnswer = currentAnswer.selectedOptions.length > 0 || currentAnswer.textAnswer.trim();

    if (!hasAnswer) {
      toast.info('No response to clear');
      return;
    }

    setShowClearConfirm(true);
  };

  const confirmClearResponse = () => {
    updateAnswer(currentQuestionIndex, {
      selectedOptions: [],
      textAnswer: ''
    });
    setShowClearConfirm(false);
    toast.success('Response cleared');
  };
  
  const submitQuiz = async () => {
    if (isSubmitting) return;

    try {
      setIsSubmitting(true);

      // Clear timer
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }

      // Prepare answers with marked for review status
      const answersWithReviewStatus = answers.map((answer, index) => ({
        ...answer,
        markedForReview: markedForReview[index] || false
      }));

      // Submit the quiz
      const attemptId = attempt._id || attempt.id;
      const response = await quizAPI.submitQuizAttempt(attemptId, { answers: answersWithReviewStatus });

      // Navigate to results page
      navigate(`/courses/${courseId}/quizzes/${quizId}/results/${attemptId}`);

      toast.success('Quiz submitted successfully!');
    } catch (err) {
      console.error('Error submitting quiz:', err);
      setError('Failed to submit quiz. Please try again.');
      toast.error('Failed to submit quiz');
      setIsSubmitting(false);
    }
  };
  
  const formatTime = (seconds) => {
    if (seconds === null || seconds === undefined || isNaN(seconds)) {
      return '0:00';
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  };
  
  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#00bcd4]"></div>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-6 py-4 rounded-lg">
        <p className="font-medium">{error}</p>
        <button
          onClick={() => navigate(`/courses/${courseId}`)}
          className="mt-4 bg-red-100 text-red-700 px-4 py-2 rounded-lg hover:bg-red-200 transition-colors"
        >
          Return to Course
        </button>
      </div>
    );
  }
  
  if (!quiz || !attempt || !quiz.questions || !Array.isArray(quiz.questions) || quiz.questions.length === 0) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-6 py-4 rounded-lg">
        <p className="font-medium">Quiz data could not be loaded.</p>
        <button
          onClick={() => navigate(`/courses/${courseId}`)}
          className="mt-4 bg-yellow-100 text-yellow-700 px-4 py-2 rounded-lg hover:bg-yellow-200 transition-colors"
        >
          Return to Course
        </button>
      </div>
    );
  }

  if (!answers || answers.length === 0 || currentQuestionIndex >= quiz.questions.length) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-6 py-4 rounded-lg">
        <p className="font-medium">Quiz answers not properly initialized.</p>
        <button
          onClick={() => startQuiz()}
          className="mt-4 bg-yellow-100 text-yellow-700 px-4 py-2 rounded-lg hover:bg-yellow-200 transition-colors"
        >
          Restart Quiz
        </button>
      </div>
    );
  }

  const currentQuestion = quiz.questions[currentQuestionIndex];
  const currentAnswer = answers[currentQuestionIndex];
  const isLastQuestion = currentQuestionIndex === quiz.questions.length - 1;

  // Additional safety check for current question and answer
  if (!currentQuestion || !currentAnswer) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-6 py-4 rounded-lg">
        <p className="font-medium">Error: Current question or answer data is missing.</p>
        <button
          onClick={() => startQuiz()}
          className="mt-4 bg-red-100 text-red-700 px-4 py-2 rounded-lg hover:bg-red-200 transition-colors"
        >
          Restart Quiz
        </button>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>

      <div className="relative flex max-w-7xl mx-auto p-4 sm:p-6 gap-6">
        {/* Question Navigation Panel - Large Screens */}
        <div className="hidden xl:block w-80 flex-shrink-0">
          <div className="sticky top-6">
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
              <div className="flex items-center gap-3 mb-6">
                <FaListAlt className="text-blue-500 text-xl" />
                <h3 className="text-lg font-bold text-gray-800">Questions</h3>
              </div>

              <div className="space-y-3 max-h-96 overflow-y-auto">
                {quiz.questions.map((question, index) => {
                  const isAnswered = answers[index].selectedOptions.length > 0 || answers[index].textAnswer;
                  const isCurrent = index === currentQuestionIndex;
                  const isMarkedForReview = markedForReview[index];

                  return (
                    <button
                      key={index}
                      onClick={() => setCurrentQuestionIndex(index)}
                      className={`w-full p-3 rounded-xl text-left transition-all duration-300 transform hover:scale-105 ${
                        isCurrent
                          ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg'
                          : isMarkedForReview
                            ? 'bg-gradient-to-r from-yellow-50 to-orange-50 border-2 border-yellow-300 text-gray-800'
                            : isAnswered
                              ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-2 border-green-300 text-gray-800'
                              : 'bg-gray-50 border-2 border-gray-200 text-gray-600 hover:border-blue-300'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className={`w-8 h-8 rounded-lg flex items-center justify-center font-bold text-sm ${
                            isCurrent
                              ? 'bg-white/20 text-white'
                              : isMarkedForReview
                                ? 'bg-yellow-500 text-white'
                                : isAnswered
                                  ? 'bg-green-500 text-white'
                                  : 'bg-gray-300 text-gray-600'
                          }`}>
                            {isMarkedForReview ? (
                              <FaBookmark size={12} />
                            ) : isAnswered ? (
                              <FaCheck size={12} />
                            ) : (
                              index + 1
                            )}
                          </div>
                          <span className="font-medium">Question {index + 1}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          {isMarkedForReview && (
                            <FaBookmark className="text-yellow-500 text-sm" />
                          )}
                          {question.points > 1 && (
                            <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">
                              {question.points} pts
                            </span>
                          )}
                        </div>
                      </div>
                    </button>
                  );
                })}
              </div>

              {/* Question Summary */}
              <div className="mt-6 pt-4 border-t border-gray-200">
                <div className="grid grid-cols-3 gap-3 text-center">
                  <div className="bg-green-50 p-3 rounded-lg">
                    <div className="text-lg font-bold text-green-600">
                      {answers.filter((ans, idx) => (ans.selectedOptions.length > 0 || ans.textAnswer) && !markedForReview[idx]).length}
                    </div>
                    <div className="text-xs text-green-600">Answered</div>
                  </div>
                  <div className="bg-yellow-50 p-3 rounded-lg">
                    <div className="text-lg font-bold text-yellow-600">
                      {markedForReview.filter(marked => marked).length}
                    </div>
                    <div className="text-xs text-yellow-600">Review</div>
                  </div>
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <div className="text-lg font-bold text-gray-600">
                      {answers.filter((ans, idx) => !ans.selectedOptions.length && !ans.textAnswer && !markedForReview[idx]).length}
                    </div>
                    <div className="text-xs text-gray-600">Unanswered</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Question Panel Toggle */}
        <button
          onClick={() => setShowQuestionPanel(true)}
          className="xl:hidden fixed top-4 right-4 z-40 bg-blue-500 text-white p-3 rounded-full shadow-lg hover:bg-blue-600 transition-colors"
        >
          <FaBars />
        </button>

        {/* Mobile Question Panel Overlay */}
        {showQuestionPanel && (
          <div className="xl:hidden fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full max-h-[80vh] overflow-hidden">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <FaListAlt className="text-blue-500 text-xl" />
                    <h3 className="text-lg font-bold text-gray-800">Questions</h3>
                  </div>
                  <button
                    onClick={() => setShowQuestionPanel(false)}
                    className="text-gray-500 hover:text-gray-700 p-2"
                  >
                    <FaTimes />
                  </button>
                </div>
              </div>

              <div className="p-6 overflow-y-auto max-h-96">
                <div className="space-y-3">
                  {quiz.questions.map((question, index) => {
                    const isAnswered = answers[index].selectedOptions.length > 0 || answers[index].textAnswer;
                    const isCurrent = index === currentQuestionIndex;
                    const isMarkedForReview = markedForReview[index];

                    return (
                      <button
                        key={index}
                        onClick={() => {
                          setCurrentQuestionIndex(index);
                          setShowQuestionPanel(false);
                        }}
                        className={`w-full p-3 rounded-xl text-left transition-all duration-300 ${
                          isCurrent
                            ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg'
                            : isMarkedForReview
                              ? 'bg-gradient-to-r from-yellow-50 to-orange-50 border-2 border-yellow-300 text-gray-800'
                              : isAnswered
                                ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-2 border-green-300 text-gray-800'
                                : 'bg-gray-50 border-2 border-gray-200 text-gray-600'
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className={`w-8 h-8 rounded-lg flex items-center justify-center font-bold text-sm ${
                              isCurrent
                                ? 'bg-white/20 text-white'
                                : isMarkedForReview
                                  ? 'bg-yellow-500 text-white'
                                  : isAnswered
                                    ? 'bg-green-500 text-white'
                                    : 'bg-gray-300 text-gray-600'
                            }`}>
                              {isMarkedForReview ? (
                                <FaBookmark size={12} />
                              ) : isAnswered ? (
                                <FaCheck size={12} />
                              ) : (
                                index + 1
                              )}
                            </div>
                            <span className="font-medium">Question {index + 1}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            {isMarkedForReview && (
                              <FaBookmark className="text-yellow-500 text-sm" />
                            )}
                            {question.points > 1 && (
                              <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">
                                {question.points} pts
                              </span>
                            )}
                          </div>
                        </div>
                      </button>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Main Content */}
        <div className="flex-1 min-w-0">
        {/* Quiz Header Card */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-4 sm:p-6 mb-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex items-center gap-3 sm:gap-4 w-full sm:w-auto">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0">
                <FaQuestionCircle className="text-white text-lg sm:text-xl" />
              </div>
              <div className="min-w-0 flex-1">
                <h2 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent truncate">
                  {quiz.title}
                </h2>
                <p className="text-gray-600 mt-1 flex items-center gap-2 text-sm sm:text-base">
                  <FaListAlt className="text-xs sm:text-sm flex-shrink-0" />
                  <span className="truncate">{quiz.questions.length} questions • {quiz.timeLimit} minutes</span>
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3 sm:gap-4 w-full sm:w-auto justify-between sm:justify-end">
              {timeLeft !== null && (
                <div className={`flex items-center gap-2 sm:gap-3 px-3 sm:px-6 py-2 sm:py-3 rounded-xl shadow-lg flex-shrink-0 ${
                  timeLeft < 60
                    ? 'bg-gradient-to-r from-red-500 to-pink-500 text-white animate-pulse shadow-red-200'
                    : timeLeft < 300
                    ? 'bg-gradient-to-r from-orange-400 to-yellow-500 text-white shadow-orange-200'
                    : 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-blue-200'
                }`}>
                  <FaClock className="text-sm sm:text-lg" />
                  <div className="text-center">
                    <div className="font-bold text-sm sm:text-lg">{formatTime(timeLeft)}</div>
                    <div className="text-xs opacity-90 hidden sm:block">remaining</div>
                  </div>
                </div>
              )}

              <button
                onClick={() => setShowConfirmSubmit(true)}
                className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-4 sm:px-6 py-2 sm:py-3 rounded-xl hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 font-medium flex items-center gap-2 text-sm sm:text-base"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-3 w-3 sm:h-4 sm:w-4 border-2 border-white border-t-transparent"></div>
                    <span className="hidden sm:inline">Submitting...</span>
                    <span className="sm:hidden">Submit</span>
                  </>
                ) : (
                  <>
                    <FaCheck className="text-xs sm:text-sm" />
                    <span className="hidden sm:inline">Submit Quiz</span>
                    <span className="sm:hidden">Submit</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </div>

      {/* Progress Bar */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-4 sm:p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-semibold text-gray-700 text-sm sm:text-base">Progress</h3>
          <span className="text-xs sm:text-sm text-gray-600">
            {currentQuestionIndex + 1} of {quiz.questions.length}
          </span>
        </div>

        <div className="w-full bg-gray-200 rounded-full h-2 sm:h-3 mb-4 overflow-hidden">
          <div
            className="h-full bg-gradient-to-r from-blue-500 to-purple-600 rounded-full transition-all duration-500 ease-out"
            style={{ width: `${((currentQuestionIndex + 1) / quiz.questions.length) * 100}%` }}
          ></div>
        </div>

        {/* Question Navigation Pills */}
        <div className="flex flex-wrap gap-1 sm:gap-2 justify-center sm:justify-start">
          {quiz.questions.map((_, index) => {
            const isAnswered = answers[index].selectedOptions.length > 0 || answers[index].textAnswer;
            const isCurrent = index === currentQuestionIndex;
            const isMarkedForReview = markedForReview[index];

            return (
              <button
                key={index}
                onClick={() => setCurrentQuestionIndex(index)}
                className={`w-8 h-8 sm:w-10 sm:h-10 rounded-lg sm:rounded-xl flex items-center justify-center text-xs sm:text-sm font-bold transition-all duration-300 transform active:scale-95 sm:hover:scale-105 touch-manipulation relative ${
                  isCurrent
                    ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg'
                    : isMarkedForReview
                      ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white shadow-md'
                      : isAnswered
                        ? 'bg-gradient-to-r from-green-400 to-emerald-500 text-white shadow-md'
                        : 'bg-white text-gray-600 border-2 border-gray-200 hover:border-blue-300 hover:bg-blue-50'
                }`}
              >
                {isMarkedForReview && !isCurrent ? (
                  <FaBookmark size={10} className="sm:w-3 sm:h-3" />
                ) : isAnswered && !isCurrent ? (
                  <FaCheck size={10} className="sm:w-3 sm:h-3" />
                ) : (
                  index + 1
                )}
                {isMarkedForReview && (
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-500 rounded-full border border-white"></div>
                )}
              </button>
            );
          })}
        </div>
      </div>

      {/* Current Question Card */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-4 sm:p-8 mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start gap-4 sm:gap-6 mb-6">
          <div className="flex items-center gap-3 sm:gap-4">
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center text-white font-bold text-base sm:text-lg flex-shrink-0">
              {currentQuestionIndex + 1}
            </div>
            <div>
              <h3 className="text-lg sm:text-xl font-bold text-gray-800">
                Question {currentQuestionIndex + 1}
              </h3>
              <p className="text-gray-600 text-sm sm:text-base">of {quiz.questions.length} questions</p>
            </div>
          </div>

          <div className="flex items-center gap-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-3 sm:px-4 py-2 rounded-xl shadow-lg self-start">
            <FaTrophy className="text-xs sm:text-sm" />
            <span className="font-bold text-sm sm:text-base">
              {currentQuestion.points} {currentQuestion.points === 1 ? 'point' : 'points'}
            </span>
          </div>
        </div>

        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 sm:p-6 rounded-xl border border-blue-100 mb-6 relative overflow-hidden">
          <div className="absolute top-0 right-0 w-24 h-24 sm:w-32 sm:h-32 bg-gradient-to-br from-blue-200/30 to-purple-200/30 rounded-full -translate-y-12 translate-x-12 sm:-translate-y-16 sm:translate-x-16"></div>
          <div className="relative">
            <div className="flex items-start gap-3">
              <FaQuestionCircle className="text-blue-500 mt-1 flex-shrink-0 text-sm sm:text-base" />
              <p className="text-gray-800 text-base sm:text-lg leading-relaxed">{currentQuestion.text}</p>
            </div>
          </div>
        </div>

        {/* Answer Options */}
        <div className="space-y-3 sm:space-y-4">
          {currentQuestion.type === 'multiple_choice' && (
            <>
              <div className="flex items-center gap-2 mb-4">
                <FaListAlt className="text-blue-500 text-sm sm:text-base" />
                <p className="text-sm font-medium text-gray-700">Select all that apply:</p>
              </div>
              {currentQuestion.options.map((option, optionIndex) => {
                const isSelected = currentAnswer.selectedOptions.includes(option._id);
                const optionLetter = String.fromCharCode(65 + optionIndex); // A, B, C, D...

                return (
                  <div
                    key={option._id}
                    onClick={() => handleOptionSelect(option._id)}
                    className={`group p-3 sm:p-4 rounded-xl border-2 cursor-pointer transition-all duration-300 transform active:scale-95 sm:hover:scale-[1.02] touch-manipulation ${
                      isSelected
                        ? 'border-blue-400 bg-gradient-to-r from-blue-50 to-indigo-50 shadow-lg shadow-blue-100'
                        : 'border-gray-200 hover:border-blue-300 hover:bg-gradient-to-r hover:from-gray-50 hover:to-blue-50 hover:shadow-md'
                    }`}
                  >
                    <div className="flex items-center gap-3 sm:gap-4">
                      <div className={`w-7 h-7 sm:w-8 sm:h-8 rounded-xl border-2 flex items-center justify-center font-bold transition-all duration-300 flex-shrink-0 ${
                        isSelected
                          ? 'bg-gradient-to-r from-blue-500 to-indigo-600 border-blue-500 text-white shadow-lg'
                          : 'border-gray-300 text-gray-600 group-hover:border-blue-400 group-hover:text-blue-600'
                      }`}>
                        {isSelected ? <FaCheck size={12} className="sm:w-3.5 sm:h-3.5" /> : optionLetter}
                      </div>
                      <span className={`text-base sm:text-lg transition-colors duration-300 leading-relaxed ${
                        isSelected ? 'text-gray-800 font-medium' : 'text-gray-700 group-hover:text-gray-800'
                      }`}>
                        {option.text}
                      </span>
                    </div>
                  </div>
                );
              })}
            </>
          )}
          
          {currentQuestion.type === 'true_false' && (
            <>
              <div className="flex items-center gap-2 mb-4">
                <FaCheck className="text-green-500 text-sm sm:text-base" />
                <p className="text-sm font-medium text-gray-700">Select one:</p>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                {currentQuestion.options.map((option, optionIndex) => {
                  const isSelected = currentAnswer.selectedOptions.includes(option._id);
                  const isTrue = option.text.toLowerCase().includes('true');

                  return (
                    <div
                      key={option._id}
                      onClick={() => handleOptionSelect(option._id)}
                      className={`group p-4 sm:p-6 rounded-xl border-2 cursor-pointer transition-all duration-300 transform active:scale-95 sm:hover:scale-105 touch-manipulation ${
                        isSelected
                          ? isTrue
                            ? 'border-green-400 bg-gradient-to-r from-green-50 to-emerald-50 shadow-lg shadow-green-100'
                            : 'border-red-400 bg-gradient-to-r from-red-50 to-pink-50 shadow-lg shadow-red-100'
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gradient-to-r hover:from-gray-50 hover:to-blue-50 hover:shadow-md'
                      }`}
                    >
                      <div className="flex items-center justify-center gap-3 sm:gap-4">
                        <div className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full border-2 flex items-center justify-center font-bold text-base sm:text-lg transition-all duration-300 flex-shrink-0 ${
                          isSelected
                            ? isTrue
                              ? 'bg-gradient-to-r from-green-500 to-emerald-600 border-green-500 text-white shadow-lg'
                              : 'bg-gradient-to-r from-red-500 to-pink-600 border-red-500 text-white shadow-lg'
                            : 'border-gray-300 text-gray-600 group-hover:border-blue-400 group-hover:text-blue-600'
                        }`}>
                          {isSelected ? <FaCheck size={14} className="sm:w-4 sm:h-4" /> : (isTrue ? 'T' : 'F')}
                        </div>
                        <span className={`text-lg sm:text-xl font-medium transition-colors duration-300 text-center sm:text-left ${
                          isSelected ? 'text-gray-800' : 'text-gray-700 group-hover:text-gray-800'
                        }`}>
                          {option.text}
                        </span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </>
          )}

          {currentQuestion.type === 'short_answer' && (
            <>
              <div className="flex items-center gap-2 mb-4">
                <FaEdit className="text-blue-500 text-sm sm:text-base" />
                <p className="text-sm font-medium text-gray-700">Enter your answer:</p>
              </div>
              <div className="relative">
                <textarea
                  value={currentAnswer.textAnswer}
                  onChange={handleTextAnswerChange}
                  className="w-full p-3 sm:p-4 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-blue-100 focus:border-blue-400 transition-all duration-300 bg-white shadow-sm resize-none text-base"
                  rows="4"
                  placeholder="Type your answer here..."
                ></textarea>
                <div className="absolute bottom-2 sm:bottom-3 right-2 sm:right-3 text-xs text-gray-400">
                  {currentAnswer.textAnswer?.length || 0} characters
                </div>
              </div>
            </>
          )}

          {currentQuestion.type === 'essay' && (
            <>
              <div className="flex items-center gap-2 mb-4">
                <FaFileAlt className="text-purple-500 text-sm sm:text-base" />
                <p className="text-sm font-medium text-gray-700">Write your essay:</p>
              </div>
              <div className="relative">
                <textarea
                  value={currentAnswer.textAnswer}
                  onChange={handleTextAnswerChange}
                  className="w-full p-3 sm:p-4 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-purple-100 focus:border-purple-400 transition-all duration-300 bg-white shadow-sm resize-none text-base"
                  rows="8"
                  placeholder="Write your essay here... Be detailed and provide examples to support your points."
                ></textarea>
                <div className="absolute bottom-2 sm:bottom-3 right-2 sm:right-3 text-xs text-gray-400">
                  {currentAnswer.textAnswer?.length || 0} characters
                </div>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-4 sm:p-6 mb-6">
        <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
          {/* Mark for Review and Clear Response */}
          <div className="flex gap-3 flex-1">
            <button
              onClick={toggleMarkForReview}
              className={`flex items-center gap-2 px-4 py-2 sm:py-3 rounded-xl font-medium transition-all duration-300 transform active:scale-95 sm:hover:scale-105 shadow-md touch-manipulation flex-1 sm:flex-none ${
                markedForReview[currentQuestionIndex]
                  ? 'bg-gradient-to-r from-yellow-500 to-orange-500 text-white hover:from-yellow-600 hover:to-orange-600'
                  : 'bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-700 hover:from-yellow-200 hover:to-orange-200 border border-yellow-300'
              }`}
            >
              <FaBookmark className="text-sm" />
              <span className="text-sm sm:text-base">
                {markedForReview[currentQuestionIndex] ? 'Unmark' : 'Mark'} Review
              </span>
            </button>

            <button
              onClick={clearCurrentResponse}
              className="flex items-center gap-2 px-4 py-2 sm:py-3 rounded-xl font-medium transition-all duration-300 transform active:scale-95 sm:hover:scale-105 shadow-md touch-manipulation bg-gradient-to-r from-red-100 to-pink-100 text-red-700 hover:from-red-200 hover:to-pink-200 border border-red-300 flex-1 sm:flex-none"
            >
              <FaTrash className="text-sm" />
              <span className="text-sm sm:text-base hidden sm:inline">Clear</span>
            </button>
          </div>
        </div>
      </div>

      {/* Navigation Buttons */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-4 sm:p-6">
        <div className="flex justify-between items-center gap-4">
          <button
            onClick={goToPreviousQuestion}
            className={`flex items-center gap-2 sm:gap-3 px-4 sm:px-6 py-2 sm:py-3 rounded-xl font-medium transition-all duration-300 transform touch-manipulation ${
              currentQuestionIndex > 0
                ? 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 hover:from-gray-200 hover:to-gray-300 active:scale-95 sm:hover:scale-105 shadow-md'
                : 'bg-gray-50 text-gray-400 cursor-not-allowed'
            }`}
            disabled={currentQuestionIndex === 0}
          >
            <FaArrowLeft className="text-sm sm:text-base" />
            <span className="text-sm sm:text-base">Previous</span>
          </button>

          <div className="flex items-center gap-2 text-xs sm:text-sm text-gray-600 text-center">
            <span className="hidden sm:inline">Question {currentQuestionIndex + 1} of {quiz.questions.length}</span>
            <span className="sm:hidden">{currentQuestionIndex + 1}/{quiz.questions.length}</span>
          </div>

          <button
            onClick={isLastQuestion ? () => setShowConfirmSubmit(true) : goToNextQuestion}
            className={`flex items-center gap-2 sm:gap-3 px-4 sm:px-6 py-2 sm:py-3 rounded-xl font-medium transition-all duration-300 transform active:scale-95 sm:hover:scale-105 shadow-lg touch-manipulation ${
              isLastQuestion
                ? 'bg-gradient-to-r from-green-500 to-emerald-600 text-white hover:from-green-600 hover:to-emerald-700 shadow-green-200'
                : 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white hover:from-blue-600 hover:to-indigo-700 shadow-blue-200'
            }`}
          >
            {isLastQuestion ? (
              <>
                <FaCheck className="text-sm sm:text-base" />
                <span className="text-sm sm:text-base">Finish Quiz</span>
              </>
            ) : (
              <>
                <span className="text-sm sm:text-base">Next</span>
                <FaArrowRight className="text-sm sm:text-base" />
              </>
            )}
          </button>
        </div>
      </div>

      {/* Confirm Submit Modal */}
      {showConfirmSubmit && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-lg w-full mx-4 transform transition-all duration-300 scale-100">
            <div className="p-4 sm:p-8">
              <div className="flex items-center gap-3 sm:gap-4 mb-4 sm:mb-6">
                <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center flex-shrink-0">
                  <FaExclamationTriangle className="text-white text-lg sm:text-2xl" />
                </div>
                <div>
                  <h3 className="text-xl sm:text-2xl font-bold text-gray-800">Submit Quiz?</h3>
                  <p className="text-gray-600 text-sm sm:text-base">Final confirmation required</p>
                </div>
              </div>

              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-3 sm:p-4 rounded-xl mb-4 sm:mb-6 border border-blue-100">
                <p className="text-gray-700 leading-relaxed text-sm sm:text-base">
                  Are you sure you want to submit your quiz? Once submitted, you won't be able to change your answers.
                  Please review your responses before proceeding.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row justify-end gap-3 sm:gap-4">
                <button
                  onClick={() => setShowConfirmSubmit(false)}
                  className="px-4 sm:px-6 py-2 sm:py-3 border-2 border-gray-300 rounded-xl text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-all duration-300 font-medium text-sm sm:text-base order-2 sm:order-1"
                >
                  Cancel
                </button>

                <button
                  onClick={submitQuiz}
                  className="px-4 sm:px-6 py-2 sm:py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl hover:from-green-600 hover:to-emerald-700 transition-all duration-300 font-medium shadow-lg transform active:scale-95 sm:hover:scale-105 flex items-center justify-center gap-2 text-sm sm:text-base order-1 sm:order-2 touch-manipulation"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 sm:h-5 sm:w-5 border-2 border-white border-t-transparent"></div>
                      <span>Submitting...</span>
                    </>
                  ) : (
                    <>
                      <FaCheck className="text-sm sm:text-base" />
                      <span>Submit Quiz</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Clear Response Confirmation Modal */}
      {showClearConfirm && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 transform transition-all duration-300 scale-100">
            <div className="p-4 sm:p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-red-400 to-pink-500 rounded-full flex items-center justify-center flex-shrink-0">
                  <FaTrash className="text-white text-lg" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-800">Clear Response?</h3>
                  <p className="text-gray-600 text-sm">This action cannot be undone</p>
                </div>
              </div>

              <div className="bg-gradient-to-r from-red-50 to-pink-50 p-3 rounded-xl mb-4 border border-red-100">
                <p className="text-gray-700 leading-relaxed text-sm">
                  Are you sure you want to clear your response for Question {currentQuestionIndex + 1}?
                  This will remove all selected answers and text.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row justify-end gap-3">
                <button
                  onClick={() => setShowClearConfirm(false)}
                  className="px-4 py-2 border-2 border-gray-300 rounded-xl text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-all duration-300 font-medium text-sm order-2 sm:order-1"
                >
                  Cancel
                </button>

                <button
                  onClick={confirmClearResponse}
                  className="px-4 py-2 bg-gradient-to-r from-red-500 to-pink-600 text-white rounded-xl hover:from-red-600 hover:to-pink-700 transition-all duration-300 font-medium shadow-lg transform active:scale-95 sm:hover:scale-105 flex items-center justify-center gap-2 text-sm order-1 sm:order-2 touch-manipulation"
                >
                  <FaTrash className="text-sm" />
                  <span>Clear Response</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
        </div>
      </div>
    </div>
  );
};

export default QuizTaker;
